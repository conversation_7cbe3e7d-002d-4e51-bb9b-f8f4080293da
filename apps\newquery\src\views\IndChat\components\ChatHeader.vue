<template>
    <div
        class="chat-header absolute top-0 left-0 right-0 z-10 flex items-center justify-between px-4 py-3 border-b border-gray-200"
    >
        <!-- 左侧：新建对话按钮 -->
        <div class="flex items-center">
            <el-tooltip effect="dark" content="新建对话" placement="bottom">
                <button
                    class="p-2 border-none bg-transparent hover:bg-gray-100 rounded-md transition-colors flex items-center justify-center"
                    @click="handleNewChat"
                >
                    <NewChatIcon />
                </button>
            </el-tooltip>
        </div>

        <!-- 中间：应用logo和名称 -->
        <div class="flex items-center">
            <!-- 应用图标 -->
            <div class="relative mr-3 flex-shrink-0">
                <div
                    v-if="app.icon_type === 'emoji'"
                    class="w-[32px] h-[32px] rounded-full flex items-center justify-center text-white text-sm font-medium"
                    :style="`background-color: ${
                        app.icon_background || '#FFEAD5'
                    }`"
                >
                    <EmojiIcon :emojiId="app.icon" :size="18" />
                </div>
                <div
                    v-else-if="app.icon_url"
                    class="w-[32px] h-[32px] rounded-full flex items-center justify-center"
                >
                    <img
                        :src="getIconUrl(app.icon_url)"
                        :alt="app.name"
                        class="w-[28px] h-[28px] object-contain rounded-full"
                    />
                </div>
                <div
                    v-else
                    class="w-[32px] h-[32px] rounded-full flex items-center justify-center text-white text-sm font-medium"
                    :style="`background-color: ${
                        app.icon_background || '#FFEAD5'
                    }`"
                >
                    {{ app.name?.charAt(0) }}
                </div>
            </div>

            <!-- 应用名称 -->
            <div class="text-lg font-medium text-gray-800">
                {{ app.name }}问答助手
            </div>
        </div>

        <!-- 右侧：历史会话按钮 -->
        <div class="flex items-center">
            <el-tooltip effect="dark" content="历史会话" placement="bottom">
                <button
                    class="p-2 border-none bg-transparent hover:bg-gray-100 rounded-md transition-colors flex items-center justify-center"
                    @click="handleHistory"
                >
                    <HistoryIcon />
                </button>
            </el-tooltip>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus';
import EmojiIcon from '@/components/EmojiIcon/index.vue';
import NewChatIcon from '@/components/icons/chat/NewChatIcon.vue';
import HistoryIcon from '@/components/icons/chat/HistoryIcon.vue';
import { getConsoleApiUrl } from '@/utils/apiUrl';

// 定义props
interface Props {
    app?: {
        id?: string;
        name?: string;
        icon?: string;
        icon_type?: string;
        icon_url?: string;
        icon_background?: string;
    };
}

const props = withDefaults(defineProps<Props>(), {
    app: () => ({
        id: 'demo',
        name: '叶片设计',
        icon: 'grinning',
        icon_type: 'emoji',
        icon_background: '#FFEAD5',
        icon_url: undefined,
    }),
});

// 定义事件
const emit = defineEmits<{
    newChat: [];
    history: [];
}>();

// 处理图标URL，如果是相对路径则拼接上API基础URL
const getIconUrl = (url: string) => {
    return getConsoleApiUrl(url);
};

// 处理新建对话点击
const handleNewChat = () => {
    // ElMessage.info('新建对话功能待实现');
    emit('newChat');
};

// 处理历史会话点击
const handleHistory = () => {
    // ElMessage.info('历史会话功能待实现');
    emit('history');
};
</script>

<style scoped>
.chat-header {
    min-height: 60px;
}
</style>
