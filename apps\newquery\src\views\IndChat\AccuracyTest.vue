<template>
    <div class="flex flex-col h-screen v3-gradient-bg relative">
        <Header :showWorkspaceButton="false" :showBackHomeButton="true" />

        <div class="flex flex-1 overflow-hidden">
            <!-- 左侧测试配置区域 -->
            <aside class="w-[400px] bg-white h-full flex flex-col border-r">
                <div class="p-4 border-b">
                    <h2 class="text-lg font-semibold text-gray-800">
                        问答精准度测试
                    </h2>
                </div>

                <div class="flex-1 overflow-y-auto p-4 space-y-4">
                    <!-- 应用选择 -->
                    <div>
                        <label
                            class="block text-sm font-medium text-gray-700 mb-2"
                        >
                            选择测试应用
                        </label>
                        <el-select
                            v-model="selectedAppId"
                            placeholder="请选择应用"
                            class="w-full"
                            @change="handleAppChange"
                        >
                            <el-option
                                v-for="app in appList"
                                :key="app.id"
                                :label="app.name"
                                :value="app.id"
                            />
                        </el-select>
                    </div>

                    <!-- 测试问题输入 -->
                    <div>
                        <label
                            class="block text-sm font-medium text-gray-700 mb-2"
                        >
                            测试问题 (JSON格式)
                        </label>
                        <el-input
                            v-model="testQuestionsJson"
                            type="textarea"
                            :rows="12"
                            placeholder='请输入JSON格式的测试问题，例如：
[
    {
        "question": "7N24.23.01.01的零件名称是什么?",
        "answer": "高压第1级动叶(左旋)"
    },
    {
        "question": "7N24.23.01.01这个零件的具体名称是什么?",
        "answer": "高压第1级动叶(左旋)"
    }
]'
                            class="w-full"
                        />
                    </div>

                    <!-- 操作按钮 -->
                    <div class="space-y-2">
                        <el-button
                            type="primary"
                            @click="startTest"
                            :disabled="!canStartTest"
                            :loading="isTestRunning"
                            class="w-full"
                        >
                            {{ isTestRunning ? '测试进行中...' : '启动测试' }}
                        </el-button>

                        <el-button
                            v-if="isTestRunning"
                            @click="stopTest"
                            class="w-full"
                        >
                            停止测试
                        </el-button>
                    </div>

                    <!-- 测试进度 -->
                    <div v-if="isTestRunning || testResults.length > 0">
                        <label
                            class="block text-sm font-medium text-gray-700 mb-2"
                        >
                            测试进度
                        </label>
                        <el-progress
                            :percentage="testProgress"
                            :status="isTestRunning ? 'active' : 'success'"
                        />
                        <div class="text-sm text-gray-600 mt-1">
                            {{ currentTestIndex }} / {{ testQuestions.length }}
                        </div>
                    </div>

                    <!-- 测试结果统计 -->
                    <div
                        v-if="testResults.length > 0 && !isTestRunning"
                        class="bg-gray-50 p-4 rounded-lg"
                    >
                        <h3 class="text-sm font-medium text-gray-700 mb-2">
                            测试结果统计
                        </h3>
                        <div class="space-y-1 text-sm">
                            <div>总问题数: {{ testResults.length }}</div>
                            <div>正确答案数: {{ correctAnswers }}</div>
                            <div>错误答案数: {{ incorrectAnswers }}</div>
                            <div class="font-semibold text-lg">
                                精准度: {{ accuracyPercentage }}%
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 右侧测试过程展示区域 -->
            <div class="flex-1 flex flex-col bg-transparent">
                <div class="p-4 border-b bg-white">
                    <h3 class="text-lg font-medium text-gray-800">测试过程</h3>
                </div>

                <div class="flex-1 overflow-y-auto p-4">
                    <div
                        v-if="testResults.length === 0 && !isTestRunning"
                        class="text-center text-gray-500 mt-20"
                    >
                        请配置测试参数并启动测试
                    </div>

                    <!-- 测试结果列表 -->
                    <div v-else class="space-y-4">
                        <div
                            v-for="(result, index) in testResults"
                            :key="index"
                            class="bg-white rounded-lg shadow-sm border p-4"
                        >
                            <div class="flex items-start justify-between mb-2">
                                <h4 class="font-medium text-gray-800">
                                    问题 {{ index + 1 }}
                                </h4>
                                <el-tag
                                    :type="
                                        result.isCorrect ? 'success' : 'danger'
                                    "
                                    size="small"
                                >
                                    {{ result.isCorrect ? '正确' : '错误' }}
                                </el-tag>
                            </div>

                            <div class="space-y-2 text-sm">
                                <div>
                                    <span class="font-medium text-gray-600"
                                        >问题:</span
                                    >
                                    <span class="ml-2">{{
                                        result.question
                                    }}</span>
                                </div>

                                <div>
                                    <span class="font-medium text-gray-600"
                                        >标准答案:</span
                                    >
                                    <span class="ml-2">{{
                                        result.standardAnswer
                                    }}</span>
                                </div>

                                <div>
                                    <span class="font-medium text-gray-600"
                                        >AI回答:</span
                                    >
                                    <div
                                        class="ml-2 mt-1 p-2 bg-gray-50 rounded text-xs"
                                    >
                                        {{ result.aiAnswer }}
                                    </div>
                                </div>

                                <div class="text-xs text-gray-500">
                                    测试时间: {{ result.timestamp }}
                                </div>
                            </div>
                        </div>

                        <!-- 当前正在测试的问题 -->
                        <div
                            v-if="isTestRunning && currentQuestion"
                            class="bg-blue-50 rounded-lg border border-blue-200 p-4"
                        >
                            <div class="flex items-center mb-2">
                                <el-icon class="is-loading text-blue-500 mr-2"
                                    ><Loading
                                /></el-icon>
                                <h4 class="font-medium text-blue-800">
                                    正在测试问题 {{ currentTestIndex }}
                                </h4>
                            </div>

                            <div class="text-sm text-blue-700">
                                <div class="mb-1">
                                    <span class="font-medium">问题:</span>
                                    <span class="ml-2">{{
                                        currentQuestion.question
                                    }}</span>
                                </div>
                                <div>
                                    <span class="font-medium">标准答案:</span>
                                    <span class="ml-2">{{
                                        currentQuestion.answer
                                    }}</span>
                                </div>
                            </div>

                            <div v-if="currentAiAnswer" class="mt-2 text-sm">
                                <span class="font-medium text-blue-700"
                                    >AI回答:</span
                                >
                                <div
                                    class="ml-2 mt-1 p-2 bg-white rounded text-xs"
                                >
                                    {{ currentAiAnswer }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup name="AccuracyTest">
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import Header from '@/components/layout/v3/Header.vue';
import {
    sendTaskmessageApi,
    validateAnswerAccuracyApi,
    getAllAppsApi,
} from '@/services/turbine-api';
import { getCurrentTime } from './utils';

// 应用列表
const appList = ref([]);
const selectedAppId = ref('');

// 测试配置
const testQuestionsJson = ref('');
const testQuestions = ref([]);

// 测试状态
const isTestRunning = ref(false);
const currentTestIndex = ref(0);
const currentQuestion = ref(null);
const currentAiAnswer = ref('');
const currentAbortController = ref(null);

// 测试结果
const testResults = ref([]);

// 计算属性
const canStartTest = computed(() => {
    return (
        selectedAppId.value &&
        testQuestionsJson.value.trim() &&
        !isTestRunning.value
    );
});

const testProgress = computed(() => {
    if (testQuestions.value.length === 0) return 0;
    return Math.round(
        (currentTestIndex.value / testQuestions.value.length) * 100
    );
});

const correctAnswers = computed(() => {
    return testResults.value.filter((result) => result.isCorrect).length;
});

const incorrectAnswers = computed(() => {
    return testResults.value.filter((result) => !result.isCorrect).length;
});

const accuracyPercentage = computed(() => {
    if (testResults.value.length === 0) return 0;
    return Math.round((correctAnswers.value / testResults.value.length) * 100);
});

// 加载应用列表
const loadAppList = async () => {
    try {
        const response = await getAllAppsApi();
        appList.value = response.data?.data || [];
    } catch (error) {
        console.error('加载应用列表失败:', error);
        ElMessage.error('加载应用列表失败');
    }
};

// 应用选择变化
const handleAppChange = (appId) => {
    selectedAppId.value = appId;
};

// 启动测试
const startTest = async () => {
    try {
        // 解析测试问题JSON
        const questions = JSON.parse(testQuestionsJson.value);
        if (!Array.isArray(questions) || questions.length === 0) {
            ElMessage.error('请输入有效的测试问题JSON数组');
            return;
        }

        // 验证问题格式
        for (const q of questions) {
            if (!q.question || !q.answer) {
                ElMessage.error('每个测试问题必须包含question和answer字段');
                return;
            }
        }

        testQuestions.value = questions;
        testResults.value = [];
        currentTestIndex.value = 0;
        isTestRunning.value = true;

        // 开始测试
        await runTest();
    } catch (error) {
        console.error('启动测试失败:', error);
        ElMessage.error('测试问题JSON格式错误');
        isTestRunning.value = false;
    }
};

// 停止测试
const stopTest = () => {
    if (currentAbortController.value) {
        currentAbortController.value.abort();
    }
    isTestRunning.value = false;
    currentQuestion.value = null;
    currentAiAnswer.value = '';
};

// 运行测试
const runTest = async () => {
    for (let i = 0; i < testQuestions.value.length; i++) {
        if (!isTestRunning.value) break;

        currentTestIndex.value = i + 1;
        currentQuestion.value = testQuestions.value[i];
        currentAiAnswer.value = '';

        try {
            // 发送问题到AI
            const aiAnswer = await askQuestion(currentQuestion.value.question);

            if (!isTestRunning.value) break;

            // 校验答案准确性
            const isCorrect = await validateAnswerAccuracyApi(
                currentQuestion.value.answer,
                aiAnswer
            );

            // 保存测试结果
            testResults.value.push({
                question: currentQuestion.value.question,
                standardAnswer: currentQuestion.value.answer,
                aiAnswer: aiAnswer,
                isCorrect: isCorrect,
                timestamp: getCurrentTime(),
            });
        } catch (error) {
            console.error(`测试问题 ${i + 1} 失败:`, error);

            // 保存错误结果
            testResults.value.push({
                question: currentQuestion.value.question,
                standardAnswer: currentQuestion.value.answer,
                aiAnswer: '测试失败: ' + error.message,
                isCorrect: false,
                timestamp: getCurrentTime(),
            });
        }
    }

    // 测试完成
    isTestRunning.value = false;
    currentQuestion.value = null;
    currentAiAnswer.value = '';

    ElMessage.success(`测试完成！精准度: ${accuracyPercentage.value}%`);
};

// 向AI提问并获取完整回答
const askQuestion = async (question) => {
    return new Promise(async (resolve, reject) => {
        try {
            // 创建新的AbortController
            currentAbortController.value = new AbortController();

            const response = await sendTaskmessageApi(
                question,
                selectedAppId.value,
                null, // topic_id
                '', // scene_token
                currentAbortController.value
            );

            if (!response || !response.getReader) {
                reject(new Error('无效的响应格式'));
                return;
            }

            const reader = response.getReader();
            const decoder = new TextDecoder();
            let done = false;
            let fullAnswer = '';

            // 处理流数据
            while (!done && isTestRunning.value) {
                const { value, done: streamDone } = await reader.read();
                done = streamDone;

                if (value) {
                    const chunk = decoder.decode(value, { stream: true });
                    const { content, error } = parseStreamData(chunk);

                    if (error === '1') {
                        reject(new Error('应用不存在或无权限'));
                        return;
                    }

                    if (content && Array.isArray(content)) {
                        content.forEach((item) => {
                            if (item.content) {
                                fullAnswer += item.content;
                                currentAiAnswer.value = fullAnswer;
                            }
                        });
                    }
                }
            }

            resolve(fullAnswer);
        } catch (error) {
            if (error.name === 'AbortError') {
                reject(new Error('测试被中断'));
            } else {
                reject(error);
            }
        }
    });
};

// 解析流数据 (简化版本，基于ChatView的实现)
const parseStreamData = (orgData) => {
    try {
        const itemList = parseStreamToJson(orgData);
        const res = itemList.map((item) => {
            const data = item;
            if (data.error) {
                return {
                    content: [{ content: '' }],
                    error: data.error,
                };
            } else {
                return data;
            }
        });

        if (res.length === 1 && res[0].error) {
            return res[0];
        } else {
            return { content: res };
        }
    } catch (error) {
        console.error('解析流数据失败:', error);
        return {
            content: [{ content: '' }],
            error: '0',
        };
    }
};

// JSON缓存
const jsonCache = ref('');

// 解析流数据为JSON
const parseStreamToJson = (dataStr) => {
    jsonCache.value += dataStr;

    const tryParseJson = (str) => {
        try {
            return JSON.parse(str);
        } catch (e) {
            return null;
        }
    };

    const results = [];
    let remaining = jsonCache.value;

    while (remaining.length > 0) {
        let found = false;

        // 尝试解析完整的JSON对象
        for (let i = 1; i <= remaining.length; i++) {
            const candidate = remaining.substring(0, i);
            const parsed = tryParseJson(candidate);

            if (parsed !== null) {
                results.push(parsed);
                remaining = remaining.substring(i);
                found = true;
                break;
            }
        }

        if (!found) {
            break;
        }
    }

    jsonCache.value = remaining;
    return results;
};

// 组件挂载时加载应用列表
onMounted(() => {
    loadAppList();
});
</script>

<style scoped>
.v3-gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.scrollbar-hide {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;
}
</style>
